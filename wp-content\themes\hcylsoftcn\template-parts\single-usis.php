<?php

/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package hcylsoftcn
 */

//get_header();
?>
<div class="wrapper-pad">
    <div class="tabs-content">
        <!-- 统一身份认证开始 -->
        <div class="about-content-main dis-block" style="min-height:600px;">
            <div class="product-box pro-box-1">
                <p class="product-title">统一软件基础平台解决方案</p>
                <p class="product-intro">一个核心的应用基础平台</p>
                <p class="product-line"></p>
                <div class="pro-name">
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-car-img pro-img-1">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/001.png" alt="" width="100%" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-13.png" alt="" width="90%" class="pad-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-13.png" alt="" width="100%" class="mb-only" />
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-car-img pro-content" style="padding-top: 10px;">
                        <p>
                            基础资源包括存储服务、计算资源、网络服务与安全服务，为门户平台提供IAAS基础资源，可以基于企业私有化部署的基础资源，也可以租用阿里云、腾讯云、华为云等云端资源。
                            门户平台包括两部分，核心的平台层，包括统一身份认证、统一用户管理、应用管理、统一门户、统一消息、平台配置、平台监控与运维，企业微信移动平台；以及用户接入访问层，通过PC端、企业微信、APP统一获取信息资源以及一站式办公，包括统一待办待阅，信息发布，应用入口以及统计分析图表等。门户平台与企业微信、腾讯短信、腾讯企业邮等进行了集成对接，与腾讯企业生态深度融合。
                            企业应用为企业的办公管理应用、业务应用及各专题领域的统计分析，以及互联网的SaaS应用等，门户平台的应用套件，包括企业管理、合同管理、印章管理、会议管理、考勤管理、规章制度、企业管理、企业通讯录、工作任务管理、办公资产、督办、考核、员工报销、问卷调查、企业论坛等。
                        </p>
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <p class="product-title">企业应用场景</p>
                <p class="product-intro">致力于打造一个核心的应用基础平台，实现对企业应用有效的整合，使应用系统之间互联互通，提升应用系统的业务价值</p>
                <p class="product-line"></p>
                <div class="pro-name">
                    <div class="col-md-12 col-sm-12 col-xs-12 application-scenarios">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/gudao.png" alt="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-14.png" alt="" width="70%" class="pad-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-14.png" alt="" width="90%" class="mb-only" />
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12 application-scenarios-discribe"
                        style="padding-top: 10px;">
                        <p>
                            随着企业业务和管理的全面深入推进，企业信息化在企业中角色越来越重要，对业务支撑和企业管控起着关键的作用。但是在应用系统建设、管理和整合的过程中，缺乏整体的信息规划和标准规范体系，形成典型的“信息孤岛”现象，造成资源不共享、数据不一致，应用系统之间数据不互通，业务不能集成，整个的业务架构体系不健全。
                        </p>
                    </div>
                </div>
            </div>
            <div class="product-box">
                <p class="product-title">产品架构</p>
                <p class="product-intro">深度融合,智能驱动每一个服务环节</p>
                <p class="product-line"></p>
                <div class="pro-name">
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jiagou.png" alt="" width="100%" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-15.png" width="100%" alt="" class="pad-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-15.png" width="100%" alt="" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <p class="product-title">核心功能</p>
                <p class="product-intro">为应用随需定制提供技术基础</p>
                <p class="product-line"></p>
                <div class="pro-name">
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-car-box">
                        <div class="pro-num">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-1.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-1.png" height="17" alt=""
                                class="mb-only" />
                            <p class="pro-intro">统一身份认证</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>统一身份认证和单点登录，实现用户身份的统一认证，为应用系统的单点登录提供支持。</p>
                            <p>统一身份认证支持数据库用户账号密码认证、LDAP认证、AD域认证、移动端认证、UKey、动态码等多种认证方式。</p>
                            <p>应用系统与单点登录集成后，在门户上配置应用的图标链接。用户身份认证通过后，在门户上可以点击应用系统的图标链接，就可以进入应用系统，实现在应用系统间的漫游。</p>
                            <p>单点登录的页面，支持统一账号密码登录，采用强密码策略确认用户认证的安全性，用户可以通过绑定的手机号发送验证码找回密码或者修改密码；也可以切换到移动端扫码登录。
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-img-4">
                        <div class="btn-container col-md-12 col-xs-12">
                            <div class="btn active">账号密码登录</div>
                            <div class="btn">移动端扫码登录</div>
                            <div class="btn">修改密码</div>
                        </div>
                        <div class="computer-img-container-pc col-md-12 pc-only">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-1.png" alt="" width="" style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-2.png" alt="" width="" style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-3.png" alt="" width="" style="margin:0 auto;" />
                        </div>
                        <div class="computer-img-container-mb pad-only pro-img-2">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-1.png" alt="" width="90%"
                                style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-2.png" alt="" width="90%"
                                style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-3.png" alt="" width="90%"
                                style="margin:0 auto;" />
                        </div>
                        <div class="computer-img-container-mb mb-only">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-1.png" alt="" width="90%"
                                style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-2.png" alt="" width="90%"
                                style="margin:0 auto;" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/c-3.png" alt="" width="90%"
                                style="margin:0 auto;" />
                        </div>

                    </div>
                </div>
            </div>
            <div class="product-box">
                <div class="pro-name">
                    <div class="col-md-6 col-sm-6 pc-pad pro-img-3">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/yhgl.png" alt="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/yhgl.png" width="90%" alt="" alt=""
                            class="pad-only" />
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-car-img">
                        <div class="pro-num pro-num-r">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-2.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-2.png" alt="" height="15"
                                class="mb-only" />
                            <p class="pro-intro">统一用户管理</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>统一组织和用户管理，对企业的组织和用户进行统一管理，实现组织、用户的存储，确保一套用户账号密码体系，实现用户的全生命周期的管理。组织用户来源企业HR系统，中间为统一用户管理，下游为企业微信、腾讯企业邮，以及应用系统等。
                            </p>
                            <p>支持从HR系统同步组织用户数据，在统一用户中进行统一管理，然后自动同步到企业微信通讯录，腾讯企业邮箱，以及分发给应用系统。支持全量和增量同步，确保企业范围内统一用户账号体系。
                            </p>
                            <p>统一用户管理支持组织（新建、修改、删除、禁用、迁移等）的各项操作，用户（新建、修改、删除、调岗、禁用等)，用户账号的（锁定、解锁等）管理的各项操作。支持基于工作流进行操作的审批。
                            </p>
                        </div>
                    </div>
                    <div class="col-xs-12 mb-only pro-img-5">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-16.png" alt="" width="100%" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <div class="pro-name">
                    <div class="col-md-5 col-sm-6 col-xs-12 pro-car-box">
                        <div class="pro-num">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-3.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-3.png" height="17" alt=""
                                class="mb-only" />
                            <p class="pro-intro">PC门户</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>统一门户作为信息聚合和工作平台的入口，集成了来自各应用系统的资源，使用户登录企业门户便可以获取信息资源，实现一站式办公。门户支持多站点，丰富的部件呈现，布局可以定制，支持更换主题等。统一待办待阅，在门户进行集中呈现各个应用系统的待办待阅项，使用户能够进行统一的办理。用户登录门户后，所有的工作项将统一进行展现办理，将大幅提高用户的工作效率。待办待阅部件自动实时刷新数据。
                            </p>
                        </div>

                    </div>
                    <div class="col-md-7 col-sm-6 col-xs-12 pro-img-6">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/menhu.png" alt="" width="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/menhu.png" alt="" width="90%" class="pad-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-17.png" alt="" width="90%" class="mb-only"
                            style="margin:0 auto;" />
                    </div>
                    <div class="pc-pad">
                        <div class="tabs col-md-12 col-sm-12">
                            <div class="tabs-ul">
                                <div class="active">多站点切换</div>
                                <div>布局定制</div>
                                <div>更换主题</div>
                                <div>统计图表</div>
                                <div>统一待办待阅</div>
                                <div>用户自定义系统入口</div>
                                <div>丰富的组件</div>
                            </div>
                            <div class="tabPane">
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content1.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content2.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content3.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content4.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content5.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content6.png" alt="">
                                </div>
                                <div>
                                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content7.png" alt="">
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- 手机端轮播样式 -->
                    <div class="swiper-container-menhu mb-only">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <p>多站点切换</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content1.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>布局定制</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content2.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>更换主题</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content3.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>统计图表</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content4.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>统一待办待阅</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content5.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>用户自定义系统入口</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content6.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <p>丰富的组件</p>
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/content7.png" alt="" class="mb-only" />
                            </div>
                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
            <div class="product-box">
                <div class="pro-name">
                    <div class="col-md-6 col-sm-6 pc-pad">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/mobileport.png" alt="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/mobileport.png" width="80%" alt="" alt=""
                            class="pad-only" />
                    </div>
                    <div class="col-md-6 col-sm-6 col-xs-12 pro-car-img">
                        <div class="pro-num pro-num-r">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-4.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-4.png" alt="" height="15"
                                class="mb-only" />
                            <p class="pro-intro">移动门户</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>移动门户基于企业微信的移动门户，统一门户、待办待阅，待办待阅消息提醒</p>
                        </div>
                    </div>
                    <div class="col-xs-12 mb-only pro-img-5">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-18.png" alt="" width="100%" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <div class="pro-name">
                    <div class="col-md-5 col-sm-5 col-xs-12 pro-car-box">
                        <div class="pro-num">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-5.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-5.png" height="17" alt=""
                                class="mb-only" />
                            <p class="pro-intro">统一消息</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>统一消息，实现消息模板的统一配置，集成云短信、企业微信、邮件等消息通道，实现对用户的统一消息推送和提醒；支持短信验证码，待办待阅提醒，消息提醒等。</p>
                        </div>
                    </div>
                    <div class="col-md-7 col-sm-7 col-xs-12 pro-img-5">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/message.png" alt="" width="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-19.png" alt="" width="90%" class="pad-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-19.png" alt="" width="100%" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box">
                <div class="pro-name">
                    <div class="col-md-8 col-sm-8 pc-pad">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/pingtai.png" alt="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/pingtai.png" width="90%" alt="" alt=""
                            class="pad-only" />
                    </div>
                    <div class="col-md-4 col-sm-4 col-xs-12 pro-car-img">
                        <div class="pro-num pro-num-r">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-6.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-6.png" alt="" height="15"
                                class="mb-only" />
                            <p class="pro-intro">平台配置</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>企业logo、版权信息、授权信息、二维码、主题皮肤等；
                                企业微信配置参数、企业邮箱配置参数、云短信配置参数。</p>
                        </div>
                    </div>
                    <div class="col-xs-12 mb-only pro-img-5">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-20.png" alt="" width="100%" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <div class="pro-name">
                    <div class="col-md-5 col-sm-5 col-xs-12 pro-car-box">
                        <div class="pro-num">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-7.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-7.png" height="17" alt=""
                                class="mb-only" />
                            <p class="pro-intro">应用管理</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>门户平台集成应用的注册，注册后生成应用用于单点登录集成的参数，设置应用的访问控制权限，如果用户没有应用的访问权限，将会提示没有权限访问的错误提示页面。</p>
                        </div>
                    </div>
                    <div class="pc-pad">
                        <div class="col-md-7 col-sm-7 col-xs-12 pro-img-4">
                            <div class="yingyong-img-container">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-1.png" alt="" width="" />
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-2.png" alt="" width="" />
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-3.png" alt="" width="" />
                            </div>
                            <div class="bottom-line-container">
                                <div class="btn-line active"></div>
                                <div class="btn-line"></div>
                                <div class="btn-line"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 手机端轮播样式 -->
                    <div class="swiper-container-menhu mb-only">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-1.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-2.png" alt="" class="mb-only" />
                            </div>
                            <div class="swiper-slide">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/y-3.png" alt="" class="mb-only" />
                            </div>

                        </div>
                        <!-- Add Pagination -->
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
            <div class="product-box">
                <div class="pro-name">
                    <div class="col-md-8 col-sm-7 pc-pad">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/api.png" alt="" class="pc-only" />
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/api.png" width="80%" alt="" alt="" class="pad-only" />
                    </div>
                    <div class="col-md-4 col-sm-5 col-xs-12 pro-car-img">
                        <div class="pro-num pro-num-r">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-8.png" alt="" class="pc-pad" />
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-8.png" alt="" height="15"
                                class="mb-only" />
                            <p class="pro-intro">API中心</p>
                            <p class="pro-line"></p>
                        </div>
                        <div class="pro-detail">
                            <p>
                                API中心对外提供API能力，确保门户平台的开放性，可扩展性和集成能力，支持企业应用系统集成接入。API中心包括在线集成规范及示例文档和API说明。</p>
                        </div>
                    </div>
                    <div class="col-xs-12 mb-only pro-img-5">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-21.png" alt="" width="100%" class="mb-only" />
                    </div>
                </div>
            </div>
            <div class="product-box product-box-bg">
                <p class="product-title">架构</p>
                <p class="product-intro">提高性能的技术</p>
                <p class="product-line"></p>
                <div class="pc-pad">
                    <div class="jiagou-item ">
                        <div class="active">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/icon1.png" alt="">
                        </div>
                        <div>
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/icon2.png" alt="">
                        </div>
                        <div>
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/icon3.png" alt="">
                        </div>
                    </div>
                    <div class="jiagou-area">
                        <div class="jiagou-area-item">
                            <div class="col-md-12 col-sm-12 col-xs-12 jiagou-container">
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <div class="jiagou-discribe">
                                        <p class="jiagou-title">技术架构</p>
                                        <p class="jiagou-content">
                                            门户平台采用开放的多层J2EE技术架构，在各个层面采用了业界主流优秀的开源软件架构，确保架构的开放性、稳定性和可扩展性。
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-8 col-sm-8 col-xs-12">
                                    <div class="img-padding">
                                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg01.png" alt="" />
                                    </div>

                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/pad/product/car/pro-1.png" alt="" width="100%" class="pad-only" /> -->
                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-1.png" alt="" width="100%" class="mb-only" /> -->
                                </div>
                            </div>
                        </div>
                        <div class="jiagou-area-item">
                            <div class="col-md-12 col-sm-12 col-xs-12 jiagou-container">
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <div class="jiagou-discribe">
                                        <p class="jiagou-title">部署架构</p>
                                        <p class="jiagou-content">
                                            在企业或者云端IT环境，部署区分为DMZ、应用区域、数据区域。API服务和管理应用采用集群部署架构，确保平台的高可用和高性能。
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-8 col-sm-8 col-xs-12">
                                    <div class="img-padding">
                                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg02.png" alt="" />
                                    </div>

                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/pad/product/car/pro-1.png" alt="" width="100%" class="pad-only" /> -->
                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-1.png" alt="" width="100%" class="mb-only" /> -->
                                </div>
                            </div>
                        </div>
                        <div class="jiagou-area-item">
                            <div class="col-md-12 col-sm-12 col-xs-12 jiagou-container">
                                <div class="col-md-4 col-sm-4 col-xs-12">
                                    <div class="jiagou-discribe">
                                        <p class="jiagou-title">安全架构</p>
                                        <p class="jiagou-content">
                                            在统一的安全规范和运维体系下，持续对门户平台的进行安全更新，包括：
                                            （1）应用运行环境的服务器、依赖的中间件和数据库的持续安全更新补丁；
                                            （2）应用的渗透测试，将应用的开发技术框架以及依赖的第三方包进行更新测试，在开发技术框架层面确保不存在如XSS漏洞、SQL注入、CSRF、文件上传下载等漏洞；
                                            （3）规范应用的运行用户、数据目录、日志目录，以及请求的访问控制等，确保应用安全可控。
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-8 col-sm-8 col-xs-12">
                                    <div class="img-padding">
                                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg03.png" alt="" />
                                    </div>

                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/pad/product/car/pro-1.png" alt="" width="100%" class="pad-only" /> -->
                                    <!-- <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/product/car/pro-1.png" alt="" width="100%" class="mb-only" /> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mb-only">
                <div class="product-box product-box-bg">
                    <div class="pro-name">
                        <div class="col-md-4 col-sm-4 col-xs-12 pro-car-img">
                            <div class="pro-num pro-num-r">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-1.png" alt="" class="pc-pad" />
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-1.png" alt="" height="15"
                                    class="mb-only" />
                                <p class="pro-intro">技术架构</p>
                                <p class="pro-line"></p>
                            </div>
                            <div class="pro-detail">
                                <p>
                                    门户平台采用开放的多层J2EE技术架构，在各个层面采用了业界主流优秀的开源软件架构，确保架构的开放性、稳定性和可扩展性。</p>
                            </div>
                        </div>
                        <div class="col-xs-12 mb-only pro-img-5">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg01.png" alt="" width="100%" class="mb-only" />
                        </div>
                    </div>
                </div>
                <div class="product-box">
                    <div class="pro-name">
                        <div class="col-md-4 col-sm-4 col-xs-12 pro-car-img">
                            <div class="pro-num pro-num-r">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-2.png" alt="" class="pc-pad" />
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-2.png" alt="" height="15"
                                    class="mb-only" />
                                <p class="pro-intro">部署架构</p>
                                <p class="pro-line"></p>
                            </div>
                            <div class="pro-detail">
                                <p>
                                    在企业或者云端IT环境，部署区分为DMZ、应用区域、数据区域。API服务和管理应用采用集群部署架构，确保平台的高可用和高性能。</p>
                            </div>
                        </div>
                        <div class="col-xs-12 mb-only pro-img-5">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg02.png" alt="" width="100%" class="mb-only" />
                        </div>
                    </div>
                </div>
                <div class="product-box product-box-bg">
                    <div class="pro-name">
                        <div class="col-md-4 col-sm-4 col-xs-12 pro-car-img">
                            <div class="pro-num pro-num-r">
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-3.png" alt="" class="pc-pad" />
                                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/product/common/pro-num-3.png" alt="" height="15"
                                    class="mb-only" />
                                <p class="pro-intro">安全架构</p>
                                <p class="pro-line"></p>
                            </div>
                            <div class="pro-detail">
                                <p>
                                    在统一的安全规范和运维体系下，持续对门户平台的进行安全更新，包括：
                                    （1）应用运行环境的服务器、依赖的中间件和数据库的持续安全更新补丁；
                                    （2）应用的渗透测试，将应用的开发技术框架以及依赖的第三方包进行更新测试，在开发技术框架层面确保不存在如XSS漏洞、SQL注入、CSRF、文件上传下载等漏洞；
                                    （3）规范应用的运行用户、数据目录、日志目录，以及请求的访问控制等，确保应用安全可控。</p>
                            </div>
                        </div>
                        <div class="col-xs-12 mb-only pro-img-5">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/solution/item01/jg03.png" alt="" width="100%" class="mb-only" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
//get_footer('footer-solution.php');
