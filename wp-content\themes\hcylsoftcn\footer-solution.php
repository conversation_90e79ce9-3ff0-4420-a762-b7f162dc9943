<?php

/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package hcylsoftcn
 */


get_template_part('template-parts/footer', 'common');
wp_footer();
?>
<script type="text/javascript">
    // banner--pc、pad、移动端
    var swiper1 = new Swiper('.swiper-container-menhu', {
        loop: true,
        pagination: {
            el: '.swiper-pagination',
        },
        autoplay: {
            delay: 4000,
        },
    });
    $(function() {
        var active = window.location.href.indexOf('?pages=') != -1 ? window.location.href.substring(window
            .location.href.indexOf('?pages=') + 7) - 1 : 0;
        $(".about-content-main").removeClass('dis-block');
        $(".about-content-main").eq(active).addClass('dis-block')
        $(".about-tabs li a").removeClass('selected');
        $(".about-tabs li a").eq(active).addClass('selected');
        $(".about-tabs li").each(function(i) {
            $(".about-tabs li").eq(i).click(function(event) {
                $(".about-tabs li a").removeClass('selected');
                $(".about-tabs li a").eq(i).addClass('selected');
                $(".about-content-main").removeClass('dis-block');
                $(".about-content-main").eq(i).addClass('dis-block')

            });
        });
        $(".mb-tabs li").each(function(i) {
            $(".mb-tabs li").eq(i).click(function(event) {
                $(".mb-tabs li a").removeClass('active');
                $(".mb-tabs li a").eq(i).addClass('active');
                $(".about-content-main").removeClass('dis-block');
                $(".about-content-main").eq(i).addClass('dis-block')

                var swiper2 = new Swiper('.swiper-container-jigou', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper3 = new Swiper('.swiper-container-guizhi', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper4 = new Swiper('.swiper-container-tian', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper5 = new Swiper('.swiper-container-huiyi', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper6 = new Swiper('.swiper-container-huiyishi', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper7 = new Swiper('.swiper-container-hetong', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper8 = new Swiper('.swiper-container-jindu', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });
                var swiper9 = new Swiper('.swiper-container-touzi', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                    },
                    autoplay: {
                        delay: 4000,
                    },
                });

            });
        });
    })
    var swiper = new Swiper('.swiper-container', {
        slidesPerView: 1.8,
        spaceBetween: 0,
        freeMode: true,
    });

    // 价值目标swiper - 移动端自动滑动
    var swiper10 = new Swiper('.swiper-container-jiazhimubiao', {
        slidesPerView: 1,
        spaceBetween: 20,
        loop: false,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
            reverseDirection: false,
        },
        speed: 800,
        allowTouchMove: true,
        centeredSlides: true,
    });

    // 调试信息
    console.log('Swiper10 初始化完成:', swiper10);
    console.log('Autoplay 状态:', swiper10.autoplay);

    // 确保自动播放启动
    if (swiper10.autoplay) {
        swiper10.autoplay.start();
        console.log('自动播放已启动');
    } else {
        console.log('自动播放未初始化');
    }

    // 添加自动播放事件监听
    swiper10.on('autoplay', function() {
        console.log('自动播放触发');
    });

    swiper10.on('autoplayStart', function() {
        console.log('自动播放开始');
    });

    swiper10.on('autoplayStop', function() {
        console.log('自动播放停止');
    });

    // 初始化状态：隐藏所有detail元素，显示第一个
    $(document).ready(function() {
        $(".value-target-detail2").hide();
        $(".value-target-detail2").eq(0).show();
        $(".value-target-item2").removeClass('active');
        $(".value-target-item2").eq(0).addClass('active');
    });

    // 监听swiper滑动事件，自动切换pro-name下的子元素
    swiper10.on('slideChange', function() {
        var activeIndex = swiper10.activeIndex;
        var slidesLength = swiper10.slides.length;
        console.log('当前活动slide索引:', activeIndex, '总slides数:', slidesLength);

        // 隐藏所有value-target-detail2元素
        $(".value-target-detail2").hide();
        // 显示对应索引的元素
        $(".value-target-detail2").eq(activeIndex).show();

        // 更新value-target-item2的active状态
        $(".value-target-item2").removeClass('active');
        $(".value-target-item2").eq(activeIndex).addClass('active');
    });

    // 监听到达最后一张slide时的事件
    swiper10.on('reachEnd', function() {
        console.log('到达最后一张slide，准备回到第一张');
        setTimeout(function() {
            swiper10.slideTo(0, 800);
        }, 3000); // 在最后一张停留3秒后回到第一张
    });

    // 保留点击事件功能
    $(document).ready(function() {
        $(".value-target-item2").each(function(i) {
            $(".value-target-item2").eq(i).click(function(event) {
                // 暂停自动播放
                swiper10.autoplay.stop();

                $(".value-target-item2").removeClass('active');
                $(".value-target-item2").eq(i).addClass('active');
                console.log('点击slide索引:', i);
                $(".value-target-detail2").hide();
                $(".value-target-detail2").eq(i).show();

                // 同步swiper到对应的slide
                swiper10.slideTo(i);

                // 2秒后重新开始自动播放
                setTimeout(function() {
                    swiper10.autoplay.start();
                }, 2000);
            });
        });
    });


    $(function() {
        //初始化，默认第一个选中
        showComputerPane(0);
        $('.btn-container').children('div').each(function(i) {
            $(this).click(function() {
                showComputerPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".btn-container div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showComputerPane(index) {
        $('.computer-img-container-pc').children('img').hide();
        $('.computer-img-container-pc').children('img').eq(index).show();
        $('.computer-img-container-mb').children('img').hide();
        $('.computer-img-container-mb').children('img').eq(index).show();
    }

    //应用
    $(function() {
        //初始化，默认第一个选中
        showApplicationPane(0);
        $('.bottom-line-container').children('div').each(function(i) {
            $(this).click(function() {
                showApplicationPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showApplicationPane(index) {
        $('.yingyong-img-container').children('img').hide();
        $('.yingyong-img-container').children('img').eq(index).show();
    }

    //机构管理
    $(function() {
        //初始化，默认第一个选中
        showOrganizationPane(0);
        $('.bottom-line-container-jigou').children('div').each(function(i) {
            $(this).click(function() {
                showOrganizationPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-jigou div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showOrganizationPane(index) {
        $('.yingyong-img-container-jigou').children('img').hide();
        $('.yingyong-img-container-jigou').children('img').eq(index).show();
    }
    //规制管理
    $(function() {
        //初始化，默认第一个选中
        showRegulationPane(0);
        $('.bottom-line-container-guizhi').children('div').each(function(i) {
            $(this).click(function() {
                showRegulationPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-guizhi div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showRegulationPane(index) {
        $('.yingyong-img-container-guizhi').children('img').hide();
        $('.yingyong-img-container-guizhi').children('img').eq(index).show();
    }
    //合同管理
    $(function() {
        //初始化，默认第一个选中
        showHetongPane(0);
        $('.bottom-line-container-hetong').children('div').each(function(i) {
            $(this).click(function() {
                showHetongPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-hetong div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showHetongPane(index) {
        $('.yingyong-img-container-hetong').children('img').hide();
        $('.yingyong-img-container-hetong').children('img').eq(index).show();
    }
    //进度管理
    $(function() {
        //初始化，默认第一个选中
        showJinduPane(0);
        $('.bottom-line-container-jindu').children('div').each(function(i) {
            $(this).click(function() {
                showJinduPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-jindu div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showJinduPane(index) {
        $('.yingyong-img-container-jindu').children('img').hide();
        $('.yingyong-img-container-jindu').children('img').eq(index).show();
    }
    //投资
    $(function() {
        //初始化，默认第一个选中
        showTouziPane(0);
        $('.bottom-line-container-touzi').children('div').each(function(i) {
            $(this).click(function() {
                showTouziPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-touzi div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showTouziPane(index) {
        $('.yingyong-img-container-touzi').children('img').hide();
        $('.yingyong-img-container-touzi').children('img').eq(index).show();
    }
    //价值目标
    $(function() {
        //初始化，默认第一个选中
        showJiazhiPane(0);
        $('.value-target-container').children('.value-target-item').each(function(i) {
            $(this).click(function() {
                showJiazhiPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".value-target-container .value-target-item").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showJiazhiPane(index) {
        $('.value-target-item').children('.value-target-item-jiantou').hide();
        $('.value-target-item').children('.value-target-item-jiantou').eq(index).show();
        $('.value-target-item-show').children('.value-target-detail1').hide();
        $('.value-target-item-show').children('.value-target-detail1').eq(index).show();
    }
    //会议督办开始
    $(function() {
        $(".dubanguanli-img-pane-1").hide()
        $(".dubanguanli-img-pane-1").eq(0).show()
        $(".duban-discribe-1").each(function(i) {
            $(".duban-discribe-1").eq(i).click(function(event) {
                $(".duban-discribe-1").removeClass('active');
                $(".duban-discribe-1").eq(i).addClass('active');
                $(".dubanguanli-img-pane-1").hide()
                $(".dubanguanli-img-pane-1").eq(i).show()
            });
        });
    })
    $(function() {
        $(".dubanguanli-img-pane-2").hide()
        $(".dubanguanli-img-pane-2").eq(0).show()
        $(".duban-discribe-2").each(function(i) {
            $(".duban-discribe-2").eq(i).click(function(event) {
                $(".duban-discribe-2").removeClass('active');
                $(".duban-discribe-2").eq(i).addClass('active');
                $(".dubanguanli-img-pane-2").hide()
                $(".dubanguanli-img-pane-2").eq(i).show()
            });
        });
    })
    $(function() {
        //初始化，默认第一个选中
        showDubanPane(0, '.dubanguanli-img-container-1');
        $('.bottom-line-container-duban-1').children('div').each(function(i) {
            $(this).click(function() {
                showDubanPane(i, '.dubanguanli-img-container-1');
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-duban-1 div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });
    $(function() {
        //初始化，默认第一个选中
        showDubanPane(0, '.dubanguanli-img-container-2');
        $('.bottom-line-container-duban-2').children('div').each(function(i) {
            $(this).click(function() {
                showDubanPane(i, '.dubanguanli-img-container-2');
                //去掉所有active状态，再给当前选中的赋予active
                $(".bottom-line-container-duban-2 div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showDubanPane(index, className) {
        $(className).children('img').hide();
        $(className).children('img').eq(index).show();
    }
    //会议督办end




    $(function() {
        //初始化，默认第一个选中
        showTabPane(0);
        $('.tabs-ul').children('div').each(function(i) {
            $(this).click(function() {
                showTabPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".tabs-ul div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showTabPane(index) {
        $('.tabPane').children('div').hide();
        $('.tabPane').children('div').eq(index).show();
    }

    $(function() {
        //初始化，默认第一个选中
        showImgPane(0);
        $('.jiagou-item').children('div').each(function(i) {
            $(this).click(function() {
                showImgPane(i);
                //去掉所有active状态，再给当前选中的赋予active
                $(".jiagou-item div").removeClass("active");
                $(this).addClass("active");
            });
        });
    });

    function showImgPane(index) {
        $('.jiagou-area').children('.jiagou-area-item').hide();
        $('.jiagou-area').children('.jiagou-area-item').eq(index).show();
    }
</script>
</body>

</html>